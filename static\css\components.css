/* Reusable UI Components */
/* Styles for buttons, cards, navbars, forms, badges */

/* Navigation Bar Component */
.navbar {
  background-color: var(--color-bg, #fff);
  border-bottom: 1px solid var(--color-gray-300, #dee2e6);
  padding: var(--space-4, 1rem) 0;
  position: relative;
  z-index: 100;
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4, 1rem);
}

.navbar-brand {
  font-size: var(--font-size-xl, 1.25rem);
  font-weight: var(--font-weight-bold, 700);
  color: var(--color-text, #333);
  text-decoration: none;
  margin: 0;
}

.navbar-brand:hover {
  color: var(--color-primary, #007bff);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-6, 1.5rem);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav a {
  color: var(--color-text, #333);
  text-decoration: none;
  font-weight: var(--font-weight-medium, 500);
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  border-radius: var(--border-radius, 0.25rem);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.navbar-nav a:hover,
.navbar-nav a:focus {
  color: var(--color-primary, #007bff);
  background-color: var(--color-gray-100, #f8f9fa);
  text-decoration: none;
}

.navbar-nav a.active {
  color: var(--color-primary, #007bff);
  font-weight: var(--font-weight-semibold, 600);
}

/* Responsive navbar */
@media screen and (max-width: 768px) {
  .navbar .container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3, 0.75rem);
  }

  .navbar-nav {
    width: 100%;
    justify-content: center;
    gap: var(--space-4, 1rem);
  }
}

/* Button Component */
.btn {
  display: inline-block;
  padding: var(--space-2-5, 0.625rem) var(--space-4, 1rem);
  margin: 0;
  font-family: inherit;
  font-size: var(--font-size-base, 1rem);
  font-weight: var(--font-weight-medium, 500);
  line-height: var(--line-height-base, 1.6);
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: var(--color-gray-100, #f8f9fa);
  border: var(--border-width, 1px) solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  color: var(--color-text, #333);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
  color: var(--color-text, #333);
  background-color: var(--color-gray-200, #e9ecef);
  border-color: var(--color-gray-400, #ced4da);
  text-decoration: none;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:active {
  background-color: var(--color-gray-300, #dee2e6);
  border-color: var(--color-gray-400, #ced4da);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.65;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button sizes */
.btn-sm {
  padding: var(--space-1-5, 0.375rem) var(--space-3, 0.75rem);
  font-size: var(--font-size-sm, 0.875rem);
  border-radius: var(--border-radius-sm, 0.125rem);
}

.btn-lg {
  padding: var(--space-3, 0.75rem) var(--space-5, 1.25rem);
  font-size: var(--font-size-lg, 1.125rem);
  border-radius: var(--border-radius-lg, 0.5rem);
}

/* Button Color Variants */
.btn-primary {
  color: var(--color-white, #fff);
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:hover {
  color: var(--color-white, #fff);
  background-color: #0056b3;
  border-color: #004085;
}

.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-primary:active {
  color: var(--color-white, #fff);
  background-color: #004085;
  border-color: #003d82;
}

.btn-secondary {
  color: var(--color-white, #fff);
  background-color: var(--color-gray-600, #6c757d);
  border-color: var(--color-gray-600, #6c757d);
}

.btn-secondary:hover {
  color: var(--color-white, #fff);
  background-color: #545b62;
  border-color: #4e555b;
}

.btn-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-secondary:active {
  color: var(--color-white, #fff);
  background-color: #4e555b;
  border-color: #47525d;
}

.btn-success {
  color: var(--color-white, #fff);
  background-color: var(--color-success, #28a745);
  border-color: var(--color-success, #28a745);
}

.btn-success:hover {
  color: var(--color-white, #fff);
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-success:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-success:active {
  color: var(--color-white, #fff);
  background-color: #1e7e34;
  border-color: #1c7430;
}

.btn-warning {
  color: var(--color-gray-900, #212529);
  background-color: var(--color-warning, #ffc107);
  border-color: var(--color-warning, #ffc107);
}

.btn-warning:hover {
  color: var(--color-gray-900, #212529);
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-warning:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-warning:active {
  color: var(--color-gray-900, #212529);
  background-color: #d39e00;
  border-color: #c69500;
}

.btn-danger {
  color: var(--color-white, #fff);
  background-color: var(--color-danger, #dc3545);
  border-color: var(--color-danger, #dc3545);
}

.btn-danger:hover {
  color: var(--color-white, #fff);
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-danger:active {
  color: var(--color-white, #fff);
  background-color: #bd2130;
  border-color: #b21f2d;
}

.btn-info {
  color: var(--color-white, #fff);
  background-color: var(--color-info, #17a2b8);
  border-color: var(--color-info, #17a2b8);
}

.btn-info:hover {
  color: var(--color-white, #fff);
  background-color: #138496;
  border-color: #117a8b;
}

.btn-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-info:active {
  color: var(--color-white, #fff);
  background-color: #117a8b;
  border-color: #10707f;
}

/* Article Cards */
.article-item,
.article-card {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, rgba(210, 215, 217, 0.75));
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--space-6, 1.5rem);
  transition: all 0.2s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.article-item:hover,
.article-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary, #007bff);
}

.article-title {
  margin: 0 0 var(--space-2, 0.5rem) 0;
  font-size: var(--font-size-lg, 1.125rem);
  line-height: var(--line-height-snug, 1.375);
  font-weight: var(--font-weight-semibold, 600);
}

.article-title a {
  color: var(--color-text, #333);
  text-decoration: none;
}

.article-title a:hover {
  color: var(--color-primary, #007bff);
  text-decoration: underline;
}

.article-meta {
  color: var(--color-text-muted, #6c757d);
  font-size: var(--font-size-sm, 0.875rem);
  margin: var(--space-2, 0.5rem) 0;
  flex-grow: 1;
}

.article-actions {
  margin-top: auto;
  padding-top: var(--space-4, 1rem);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Bias Indicators */
.bias-indicator {
  display: inline-block;
  padding: var(--space-1, 0.25rem) var(--space-3, 0.75rem);
  border-radius: var(--border-radius-md, 0.375rem);
  font-size: var(--font-size-xs, 0.75rem);
  font-weight: var(--font-weight-semibold, 600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.bias-left {
  background-color: rgba(13, 110, 253, 0.1);
  color: var(--color-bias-left, #0d6efd);
  border: 1px solid rgba(13, 110, 253, 0.25);
}

.bias-center {
  background-color: rgba(108, 117, 125, 0.15);
  color: #495057;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.bias-right {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--color-bias-right, #dc3545);
  border: 1px solid rgba(220, 53, 69, 0.25);
}

/* Filter Section */
.filter-section {
  background: var(--color-gray-100, #f8f9fa);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--space-6, 1.5rem);
  margin-bottom: var(--space-8, 2rem);
}

/* Results Summary */
.results-summary {
  color: var(--color-text-muted, #6c757d);
  font-size: var(--font-size-sm, 0.875rem);
  margin: var(--space-4, 1rem) 0 var(--space-8, 2rem) 0;
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  background: rgba(248, 249, 250, 0.5);
  border-radius: var(--border-radius-md, 0.375rem);
  border-left: 3px solid var(--color-primary, #007bff);
}

/* HTMX Loading States */
.htmx-indicator {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.htmx-request .htmx-indicator {
  opacity: 1;
}

.htmx-request.htmx-indicator {
  opacity: 1;
}

/* Source Management Components */
.source-management-section {
  margin: var(--space-6, 1.5rem) 0;
  padding: var(--space-6, 1.5rem);
  background: var(--color-bg, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.375rem);
}

.source-management-container {
  width: 100%;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4, 1rem);
  padding-bottom: var(--space-3, 0.75rem);
  border-bottom: 1px solid var(--color-gray-300, #dee2e6);
}

.source-header h4 {
  margin: 0;
  color: var(--color-text, #333);
}

.source-form-container {
  margin-bottom: var(--space-4, 1rem);
}

.source-form {
  background: var(--color-gray-50, #f8f9fa);
  padding: var(--space-4, 1rem);
  border-radius: var(--border-radius, 0.375rem);
  border: 1px solid var(--color-gray-300, #dee2e6);
}

.source-form h5 {
  margin: 0 0 var(--space-4, 1rem) 0;
  color: var(--color-text, #333);
}

.source-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3, 0.75rem);
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-4, 1rem);
  background: var(--color-bg, #fff);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.375rem);
  transition: box-shadow 0.2s ease;
}

.source-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.source-info {
  flex: 1;
  margin-right: var(--space-4, 1rem);
}

.source-name {
  display: flex;
  align-items: center;
  gap: var(--space-2, 0.5rem);
  margin-bottom: var(--space-2, 0.5rem);
}

.source-name strong {
  font-size: var(--font-size-lg, 1.125rem);
  color: var(--color-text, #333);
}

.source-url {
  margin-bottom: var(--space-2, 0.5rem);
}

.source-url a {
  color: var(--color-primary, #007bff);
  text-decoration: none;
  font-size: var(--font-size-sm, 0.875rem);
  word-break: break-all;
}

.source-url a:hover {
  text-decoration: underline;
}

.source-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3, 0.75rem);
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-gray-600, #6c757d);
}

.source-actions {
  display: flex;
  gap: var(--space-2, 0.5rem);
  flex-shrink: 0;
}

.error-streak {
  color: var(--color-danger, #dc3545);
  font-weight: var(--font-weight-semibold, 600);
}

.no-sources {
  text-align: center;
  padding: var(--space-8, 2rem);
  color: var(--color-gray-600, #6c757d);
}

/* Source Stats Modal */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: var(--color-bg, #fff);
  margin: 5% auto;
  padding: var(--space-6, 1.5rem);
  border-radius: var(--border-radius, 0.375rem);
  width: 90%;
  max-width: 600px;
  position: relative;
}

.close {
  position: absolute;
  right: var(--space-4, 1rem);
  top: var(--space-4, 1rem);
  color: var(--color-gray-500, #adb5bd);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: var(--color-text, #333);
}

.source-stats h5 {
  margin: 0 0 var(--space-4, 1rem) 0;
  color: var(--color-text, #333);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3, 0.75rem);
  margin-bottom: var(--space-4, 1rem);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: var(--space-2, 0.5rem);
  background: var(--color-gray-50, #f8f9fa);
  border-radius: var(--border-radius, 0.375rem);
}

.stat-label {
  font-weight: var(--font-weight-semibold, 600);
  color: var(--color-gray-700, #495057);
}

.stat-value {
  color: var(--color-text, #333);
}

.status-enabled {
  color: var(--color-success, #28a745);
  font-weight: var(--font-weight-semibold, 600);
}

.status-disabled {
  color: var(--color-warning, #ffc107);
  font-weight: var(--font-weight-semibold, 600);
}

.stats-actions {
  text-align: center;
  padding-top: var(--space-4, 1rem);
  border-top: 1px solid var(--color-gray-300, #dee2e6);
}

/* Badge styles for source management */
.badge-rss {
  background-color: var(--color-info, #17a2b8);
  color: white;
}

.badge-telegram {
  background-color: #08c;
  color: white;
}

.badge-twitter {
  background-color: #1da1f2;
  color: white;
}

.badge-reddit {
  background-color: #ff4500;
  color: white;
}

.badge-left {
  background-color: var(--color-primary, #007bff);
  color: white;
}

.badge-center {
  background-color: var(--color-secondary, #6c757d);
  color: white;
}

.badge-right {
  background-color: var(--color-danger, #dc3545);
  color: white;
}

.badge-disabled {
  background-color: var(--color-warning, #ffc107);
  color: var(--color-text, #333);
}

/* Responsive adjustments */
@media screen and (max-width: 736px) {
  .filter-section {
    padding: var(--space-4, 1rem);
  }

  .source-item {
    flex-direction: column;
    gap: var(--space-3, 0.75rem);
  }

  .source-info {
    margin-right: 0;
  }

  .source-actions {
    align-self: stretch;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
}
