<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NewsBalancer - Articles</title>
    <!-- HTMX CDN -->
    <script src="https://unpkg.com/htmx.org@1.9.10"
            integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX37iInKRy5xLSi8nO7UC"
            crossorigin="anonymous"></script>
    <!-- HTMX Extensions -->
    <script src="https://unpkg.com/htmx.org@1.9.10/dist/ext/loading-states.js"
            integrity="sha384-v04dReCP6N+wBCc+JjDUHyvkWJPO5jyzXxNdZHF/HZVyMXhh2USfi3UvCfiPwmmB"
            crossorigin="anonymous"></script>
    <link rel="stylesheet" href="/static/css/app-consolidated.css?v=1" />

            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f8f9fa;
        }
        header {
            background-color: #fff;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 2px solid #e3e3e3;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        h1 {
            color: #333;
            margin: 0;
        }
        nav a {
            color: #007bff;
            text-decoration: none;
            margin-right: 15px;
            font-weight: 500;
        }
        nav a:hover {
            text-decoration: underline;
        }
        .filter-form {
            margin-bottom: 20px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            border: 1px solid #e3e3e3;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .filter-form select, .filter-form input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }
        .filter-form input[type="text"] {
            min-width: 250px;
        }
        .filter-form button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .filter-form button:hover {
            background-color: #0069d9;
        }
        .clear-btn {
            background-color: #6c757d !important;
        }
        .clear-btn:hover {
            background-color: #545b62 !important;
        }
        .results-summary {
            margin: 15px 0;
            color: #6c757d;
            font-size: 14px;
            background-color: #fff;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e3e3e3;
        }
        .article-list {
            background-color: #fff;
            border-radius: 8px;
            border: 1px solid #e3e3e3;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .article-item {
            padding: 20px;
            border-bottom: 1px solid #e3e3e3;
            transition: background-color 0.2s ease;
        }
        .article-item:last-child {
            border-bottom: none;
        }
        .article-item:hover {
            background-color: #f8f9fa;
        }
        .article-title {
            margin-bottom: 10px;
        }
        .article-title a {
            color: #333;
            text-decoration: none;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.4;
        }
        .article-title a:hover {
            color: #007bff;
        }
        .article-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #6c757d;
        }
        .bias-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .bias-left {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .bias-center {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .bias-right {
            background-color: #ffebee;
            color: #d32f2f;
        }
        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 5px;
        }
        .pagination a {
            padding: 10px 15px;
            border: 1px solid #dee2e6;
            color: #007bff;
            text-decoration: none;
            border-radius: 6px;
            background-color: #fff;
            transition: all 0.2s ease;
        }
        .pagination a:hover {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pagination .active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pagination .disabled {
            color: #6c757d;
            cursor: not-allowed;
            background-color: #f8f9fa;
        }
        .pagination .disabled:hover {
            background-color: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
        }
        
        /* Loading states */
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: inline;
        }
        .htmx-request.htmx-indicator {
            display: block;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Screen reader only content */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        .content-container {
            position: relative;
        }
    </style>
</head>
<body>    <header role="banner">
        <div class="container">
            <h1>NewsBalancer</h1>
            <nav role="navigation" aria-label="Main navigation">
                <a href="/articles" hx-get="/articles" hx-target="body" hx-push-url="true" aria-current="{{if eq .CurrentPage "articles"}}page{{end}}">Articles</a> |
                <a href="/admin" hx-get="/admin" hx-target="body" hx-push-url="true" aria-current="{{if eq .CurrentPage "admin"}}page{{end}}">Admin</a>
            </nav>
        </div>
    </header>    <div class="container">
        <main role="main" id="main-content">
            <h1 id="articles-heading">Latest News Articles</h1>
            
            <!-- Enhanced filter form with HTMX -->
            <form class="filter-form" 
                  role="search"
                  aria-label="Filter and search articles"
                  hx-get="/api/fragments/articles" 
                  hx-target="#content-area" 
                  hx-trigger="submit, change from:select, input from:input[name='query'] delay:500ms"
                  hx-indicator="#loading-indicator">
                <div class="filter-row">
                    <label for="source-select" class="sr-only">Filter by source</label>
                    <select id="source-select" name="source" aria-label="Filter by news source">
                        <option value="">All Sources</option>
                        {{range .Sources}}
                        <option value="{{.}}" {{if eq . $.SelectedSource}}selected{{end}}>{{.}}</option>
                        {{end}}
                    </select>
                    
                    <label for="bias-select" class="sr-only">Filter by bias</label>
                    <select id="bias-select" name="bias" aria-label="Filter by political bias">
                        <option value="">All Bias Levels</option>
                        <option value="left" {{if eq .SelectedBias "left"}}selected{{end}}>Left Leaning</option>
                        <option value="center" {{if eq .SelectedBias "center"}}selected{{end}}>Center</option>
                        <option value="right" {{if eq .SelectedBias "right"}}selected{{end}}>Right Leaning</option>
                    </select>
                      <label for="search-input" class="sr-only">Search articles</label>
                    <input type="text" 
                           id="search-input"
                           data-testid="search-input"
                           name="query" 
                           placeholder="Search articles..." 
                           value="{{.SearchQuery}}"
                           aria-label="Search articles by title or content"
                           aria-describedby="search-help">
                    <div id="search-help" class="sr-only">Enter keywords to find relevant articles</div>
                    
                    <button type="button" 
                            class="clear-btn" 
                            onclick="clearFilters()"
                            aria-label="Clear all filters and search terms">Clear</button>
                </div>
            </form>              <!-- Content area that will be updated via HTMX -->
            <div id="content-area" 
                 data-testid="articles-container"
                 class="content-container"
                 role="region"
                 aria-label="Article search results"
                 aria-live="polite"
                 aria-describedby="articles-heading">
                <!-- Loading indicator -->
                <div id="loading-indicator" 
                     class="htmx-indicator loading-overlay"
                     role="status"
                     aria-live="polite"
                     aria-label="Loading content">
                    <div class="spinner"></div>
                    <span class="sr-only">Loading articles, please wait...</span>
                </div>
                  <!-- Initial content -->
                {{range .Articles}}
                <div class="article-item" data-testid="article-card-{{.ID}}" data-article-id="{{.ID}}">
                    <div class="article-title">
                        <a href="/article/{{.ID}}" data-testid="article-link-{{.ID}}">{{.Title}}</a>
                    </div>
                    <div class="article-meta">
                        <div>Source: {{.Source}}</div>
                        <div>Published: {{.PubDate.Format "2006-01-02 15:04"}}</div>
                    </div>
                    <div>
                        {{if eq .Bias "left"}}
                        <span class="bias-indicator bias-left">Left Leaning</span>
                        {{else if eq .Bias "center"}}
                        <span class="bias-indicator bias-center">Center</span>
                        {{else if eq .Bias "right"}}
                        <span class="bias-indicator bias-right">Right Leaning</span>
                        {{end}}
                    </div>
                </div>
                {{else}}
                <p data-testid="no-results">No articles found. Try adjusting your filters.</p>
                {{end}}
            </div>
        </main>
    </div>

    <script>
        // Clear filters function
        function clearFilters() {
            const form = document.querySelector('.filter-form');
            form.reset();
            htmx.trigger(form, 'submit');
        }
        
        // HTMX event listeners for enhanced UX
        document.addEventListener('htmx:beforeRequest', function(evt) {
            console.log('Loading articles...');
        });
        
        document.addEventListener('htmx:afterRequest', function(evt) {
            console.log('Articles loaded');
        });
        
        document.addEventListener('htmx:responseError', function(evt) {
            console.error('Error loading articles:', evt.detail.xhr.status);
            document.getElementById('content-area').innerHTML = 
                '<div class="alert alert-danger">Error loading articles. Please try again.</div>';
        });
    </script>
</body>
</html>
